//
//  MouseEventCapture.swift
//  MousePro
//
//  Created by lincoo on 7/14/25.
//

import Foundation
import Carbon
import Cocoa

/// 鼠标事件捕获器
/// 负责监听本机鼠标输入并转换为HID协议数据
class MouseEventCapture: ObservableObject {

    // MARK: - Properties

    @Published var isCapturing: Bool = false
    @Published var lastMouseEvent: String = ""
    @Published var mousePosition: CGPoint = .zero

    private var eventTap: CFMachPort?
    private var runLoopSource: CFRunLoopSource?
    private weak var hidManager: USBHIDManager?

    // MARK: - Initialization

    init(hidManager: USBHIDManager) {
        self.hidManager = hidManager
    }

    deinit {
        stopCapturing()
    }

    // MARK: - Public Methods

    /// 开始捕获鼠标事件
    func startCapturing() -> Bool {
        guard !isCapturing else { return true }

        // 请求辅助功能权限
        if !AXIsProcessTrusted() {
            let alert = NSAlert()
            alert.messageText = "需要辅助功能权限"
            alert.informativeText = "请在系统偏好设置 > 安全性与隐私 > 隐私 > 辅助功能中添加此应用"
            alert.addButton(withTitle: "打开系统偏好设置")
            alert.addButton(withTitle: "取消")

            if alert.runModal() == .alertFirstButtonReturn {
                NSWorkspace.shared.open(URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility")!)
            }
            return false
        }

        // 创建事件监听器 - 监听所有鼠标事件
        var eventMask: UInt64 = 0
        eventMask |= (1 << CGEventType.leftMouseDown.rawValue)
        eventMask |= (1 << CGEventType.leftMouseUp.rawValue)
        eventMask |= (1 << CGEventType.rightMouseDown.rawValue)
        eventMask |= (1 << CGEventType.rightMouseUp.rawValue)
        eventMask |= (1 << CGEventType.otherMouseDown.rawValue)
        eventMask |= (1 << CGEventType.otherMouseUp.rawValue)
        eventMask |= (1 << CGEventType.mouseMoved.rawValue)
        eventMask |= (1 << CGEventType.leftMouseDragged.rawValue)
        eventMask |= (1 << CGEventType.rightMouseDragged.rawValue)
        eventMask |= (1 << CGEventType.otherMouseDragged.rawValue)
        eventMask |= (1 << CGEventType.scrollWheel.rawValue)

        eventTap = CGEvent.tapCreate(
            tap: .cgSessionEventTap,
            place: .headInsertEventTap,
            options: .defaultTap,
            eventsOfInterest: CGEventMask(eventMask),
            callback: mouseEventCallback,
            userInfo: Unmanaged.passUnretained(self).toOpaque()
        )

        guard let eventTap = eventTap else {
            print("Failed to create mouse event tap")
            return false
        }

        runLoopSource = CFMachPortCreateRunLoopSource(kCFAllocatorDefault, eventTap, 0)
        CFRunLoopAddSource(CFRunLoopGetCurrent(), runLoopSource, .commonModes)
        CGEvent.tapEnable(tap: eventTap, enable: true)

        isCapturing = true
        print("Mouse capture started")
        return true
    }

    /// 停止捕获鼠标事件
    func stopCapturing() {
        guard isCapturing else { return }

        if let eventTap = eventTap {
            CGEvent.tapEnable(tap: eventTap, enable: false)
            CFRunLoopRemoveSource(CFRunLoopGetCurrent(), runLoopSource, .commonModes)
            CFMachPortInvalidate(eventTap)
        }

        eventTap = nil
        runLoopSource = nil
        isCapturing = false
        print("Mouse capture stopped")
    }

    // MARK: - Event Handling

    func handleMouseEvent(_ event: CGEvent) -> CGEvent? {
        let eventType = event.type
        let location = event.location

        // 更新鼠标位置
        DispatchQueue.main.async {
            self.mousePosition = location
        }

        switch eventType {
        case .leftMouseDown, .leftMouseUp:
            handleMouseButton(button: 1, isPressed: eventType == .leftMouseDown)

        case .rightMouseDown, .rightMouseUp:
            handleMouseButton(button: 2, isPressed: eventType == .rightMouseDown)

        case .otherMouseDown, .otherMouseUp:
            let buttonNumber = event.getIntegerValueField(.mouseEventButtonNumber)
            handleMouseButton(button: Int(buttonNumber), isPressed: eventType == .otherMouseDown)

        case .mouseMoved, .leftMouseDragged, .rightMouseDragged, .otherMouseDragged:
            handleMouseMovement(location: location)

        case .scrollWheel:
            let deltaY = event.getIntegerValueField(.scrollWheelEventDeltaAxis1)
            let deltaX = event.getIntegerValueField(.scrollWheelEventDeltaAxis2)
            handleScrollWheel(deltaX: Int(deltaX), deltaY: Int(deltaY))

        default:
            break
        }

        return event
    }

    private func handleMouseButton(button: Int, isPressed: Bool) {
        DispatchQueue.main.async {
            self.lastMouseEvent = "Button \(button) \(isPressed ? "Down" : "Up")"
        }

        if let hidReport = convertToHIDMouseButtonReport(button: button, isPressed: isPressed) {
            _ = hidManager?.sendHIDReport(hidReport)
        }
    }

    private func handleMouseMovement(location: CGPoint) {
        DispatchQueue.main.async {
            self.lastMouseEvent = "Move to (\(Int(location.x)), \(Int(location.y)))"
        }

        if let hidReport = convertToHIDMouseMovementReport(x: Int(location.x), y: Int(location.y)) {
            _ = hidManager?.sendHIDReport(hidReport)
        }
    }

    private func handleScrollWheel(deltaX: Int, deltaY: Int) {
        DispatchQueue.main.async {
            self.lastMouseEvent = "Scroll (\(deltaX), \(deltaY))"
        }

        if let hidReport = convertToHIDScrollReport(deltaX: deltaX, deltaY: deltaY) {
            _ = hidManager?.sendHIDReport(hidReport)
        }
    }

    // MARK: - HID Protocol Conversion

    private func convertToHIDMouseButtonReport(button: Int, isPressed: Bool) -> Data? {
        // HID鼠标按键报告格式：
        // Byte 0: Report ID
        // Byte 1: 按键状态 (bit 0: 左键, bit 1: 右键, bit 2: 中键, etc.)
        // Byte 2-3: X轴相对移动
        // Byte 4-5: Y轴相对移动
        // Byte 6: 滚轮

        var report = Data(count: 7)
        report[0] = 0x02 // Mouse Report ID

        var buttonMask: UInt8 = 0
        if isPressed {
            switch button {
            case 1: buttonMask |= 0x01 // 左键
            case 2: buttonMask |= 0x02 // 右键
            case 3: buttonMask |= 0x04 // 中键
            default: break
            }
        }

        report[1] = buttonMask
        return report
    }

    private func convertToHIDMouseMovementReport(x: Int, y: Int) -> Data? {
        // HID鼠标移动报告格式
        var report = Data(count: 7)
        report[0] = 0x02 // Mouse Report ID

        // 将绝对坐标转换为相对移动（这里简化处理）
        // 实际应用中需要计算相对于上次位置的偏移
        let deltaX = Int16(x) // 简化处理，实际需要计算差值
        let deltaY = Int16(y)

        // 将16位值分解为两个8位字节
        report[2] = UInt8(deltaX & 0xFF)
        report[3] = UInt8((deltaX >> 8) & 0xFF)
        report[4] = UInt8(deltaY & 0xFF)
        report[5] = UInt8((deltaY >> 8) & 0xFF)

        return report
    }

    private func convertToHIDScrollReport(deltaX: Int, deltaY: Int) -> Data? {
        // HID滚轮报告格式
        var report = Data(count: 7)
        report[0] = 0x02 // Mouse Report ID

        // 滚轮数据通常是8位有符号整数
        report[6] = UInt8(bitPattern: Int8(max(-127, min(127, deltaY))))

        return report
    }
}

// MARK: - C Callback

private func mouseEventCallback(
    proxy: CGEventTapProxy,
    type: CGEventType,
    event: CGEvent,
    refcon: UnsafeMutableRawPointer?
) -> Unmanaged<CGEvent>? {

    guard let refcon = refcon else { return Unmanaged.passUnretained(event) }

    let capture = Unmanaged<MouseEventCapture>.fromOpaque(refcon).takeUnretainedValue()

    if let modifiedEvent = capture.handleMouseEvent(event) {
        return Unmanaged.passUnretained(modifiedEvent)
    }

    return nil
}
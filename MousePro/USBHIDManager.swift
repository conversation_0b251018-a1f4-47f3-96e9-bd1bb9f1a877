//
//  USBHIDManager.swift
//  MousePro
//
//  Created by lincoo on 7/14/25.
//

import Foundation
import IOKit
import IOKit.hid
import IOKit.usb

/// USB HID设备管理器
/// 负责USB设备的发现、连接和通信
class USBHIDManager: ObservableObject {

    // MARK: - Properties

    @Published var connectedDevices: [USBDevice] = []
    @Published var isConnected: Bool = false
    @Published var connectionStatus: String = "未连接"

    private var hidManager: IOHIDManager?
    private var currentDevice: IOHIDDevice?

    // MARK: - Initialization

    init() {
        setupHIDManager()
    }

    deinit {
        disconnect()
    }

    // MARK: - Public Methods

    /// 开始扫描USB设备
    func startScanning() {
        guard let manager = hidManager else { return }

        IOHIDManagerScheduleWithRunLoop(manager, CFRunLoopGetCurrent(), CFRunLoopMode.defaultMode.rawValue)
        IOHIDManagerOpen(manager, IOOptionBits(kIOHIDOptionsTypeNone))

        updateConnectionStatus("正在扫描设备...")
    }

    /// 停止扫描
    func stopScanning() {
        guard let manager = hidManager else { return }

        IOHIDManagerClose(manager, IOOptionBits(kIOHIDOptionsTypeNone))
        IOHIDManagerUnscheduleFromRunLoop(manager, CFRunLoopGetCurrent(), CFRunLoopMode.defaultMode.rawValue)

        updateConnectionStatus("已停止扫描")
    }

    /// 连接到指定设备
    func connect(to device: USBDevice) {
        // 实现设备连接逻辑
        currentDevice = device.hidDevice
        isConnected = true
        updateConnectionStatus("已连接到 \(device.name)")
    }

    /// 断开连接
    func disconnect() {
        currentDevice = nil
        isConnected = false
        updateConnectionStatus("已断开连接")
    }

    /// 发送HID报告
    func sendHIDReport(_ report: Data) -> Bool {
        guard let device = currentDevice else { return false }

        let result = IOHIDDeviceSetReport(
            device,
            kIOHIDReportTypeOutput,
            CFIndex(report[0]), // Report ID
            report.withUnsafeBytes { $0.bindMemory(to: UInt8.self).baseAddress! },
            report.count
        )

        return result == kIOReturnSuccess
    }

    // MARK: - Private Methods

    private func setupHIDManager() {
        hidManager = IOHIDManagerCreate(kCFAllocatorDefault, IOOptionBits(kIOHIDOptionsTypeNone))

        guard let manager = hidManager else {
            print("Failed to create HID Manager")
            return
        }

        // 设置设备匹配条件 - 查找键盘和鼠标设备
        let deviceMatching = [
            [
                kIOHIDDeviceUsagePageKey: kHIDPage_GenericDesktop,
                kIOHIDDeviceUsageKey: kHIDUsage_GD_Keyboard
            ],
            [
                kIOHIDDeviceUsagePageKey: kHIDPage_GenericDesktop,
                kIOHIDDeviceUsageKey: kHIDUsage_GD_Mouse
            ]
        ]

        IOHIDManagerSetDeviceMatchingMultiple(manager, deviceMatching as CFArray)

        // 设置设备匹配回调
        IOHIDManagerRegisterDeviceMatchingCallback(manager, deviceMatchingCallback, Unmanaged.passUnretained(self).toOpaque())
        IOHIDManagerRegisterDeviceRemovalCallback(manager, deviceRemovalCallback, Unmanaged.passUnretained(self).toOpaque())
    }

    private func updateConnectionStatus(_ status: String) {
        DispatchQueue.main.async {
            self.connectionStatus = status
        }
    }
}

// MARK: - USB Device Model

struct USBDevice: Identifiable, Hashable {
    let id = UUID()
    let name: String
    let vendorID: Int
    let productID: Int
    let hidDevice: IOHIDDevice

    var description: String {
        return "\(name) (VID: \(String(format: "%04X", vendorID)), PID: \(String(format: "%04X", productID)))"
    }
}

// MARK: - C Callbacks

private func deviceMatchingCallback(context: UnsafeMutableRawPointer?, result: IOReturn, sender: UnsafeMutableRawPointer?, device: IOHIDDevice) {
    let manager = Unmanaged<USBHIDManager>.fromOpaque(context!).takeUnretainedValue()
    manager.handleDeviceMatching(device: device)
}

private func deviceRemovalCallback(context: UnsafeMutableRawPointer?, result: IOReturn, sender: UnsafeMutableRawPointer?, device: IOHIDDevice) {
    let manager = Unmanaged<USBHIDManager>.fromOpaque(context!).takeUnretainedValue()
    manager.handleDeviceRemoval(device: device)
}

// MARK: - Device Handling Extension

extension USBHIDManager {

    func handleDeviceMatching(device: IOHIDDevice) {
        let vendorID = IOHIDDeviceGetProperty(device, kIOHIDVendorIDKey as CFString) as? Int ?? 0
        let productID = IOHIDDeviceGetProperty(device, kIOHIDProductIDKey as CFString) as? Int ?? 0
        let productName = IOHIDDeviceGetProperty(device, kIOHIDProductKey as CFString) as? String ?? "Unknown Device"

        let usbDevice = USBDevice(
            name: productName,
            vendorID: vendorID,
            productID: productID,
            hidDevice: device
        )

        DispatchQueue.main.async {
            self.connectedDevices.append(usbDevice)
            self.updateConnectionStatus("发现设备: \(usbDevice.name)")
        }
    }

    func handleDeviceRemoval(device: IOHIDDevice) {
        DispatchQueue.main.async {
            self.connectedDevices.removeAll { $0.hidDevice == device }
            if self.currentDevice == device {
                self.disconnect()
            }
        }
    }
}
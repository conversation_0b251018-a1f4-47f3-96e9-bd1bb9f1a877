//
//  ContentView.swift
//  MousePro
//
//  Created by lincoo on 7/14/25.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var hidManager = USBHIDManager()
    @StateObject private var keyboardCapture: KeyboardEventCapture
    @StateObject private var mouseCapture: MouseEventCapture

    @State private var selectedDevice: USBDevice?
    @State private var isCapturingEnabled = false

    init() {
        let hidManager = USBHIDManager()
        self._hidManager = StateObject(wrappedValue: hidManager)
        self._keyboardCapture = StateObject(wrappedValue: KeyboardEventCapture(hidManager: hidManager))
        self._mouseCapture = StateObject(wrappedValue: MouseEventCapture(hidManager: hidManager))
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                HStack {
                    Image(systemName: "keyboard")
                        .font(.largeTitle)
                        .foregroundColor(.blue)
                    Text("MousePro")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                }
                .padding(.top)

                // 连接状态
                VStack(alignment: .leading, spacing: 10) {
                    Text("连接状态")
                        .font(.headline)

                    HStack {
                        Circle()
                            .fill(hidManager.isConnected ? Color.green : Color.red)
                            .frame(width: 12, height: 12)
                        Text(hidManager.connectionStatus)
                            .foregroundColor(.secondary)
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)

                // 设备列表
                VStack(alignment: .leading, spacing: 10) {
                    Text("可用设备")
                        .font(.headline)

                    if hidManager.connectedDevices.isEmpty {
                        Text("未发现设备")
                            .foregroundColor(.secondary)
                            .italic()
                    } else {
                        ForEach(hidManager.connectedDevices) { device in
                            HStack {
                                VStack(alignment: .leading) {
                                    Text(device.name)
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                    Text(device.description)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }

                                Spacer()

                                Button(selectedDevice?.id == device.id ? "已连接" : "连接") {
                                    if selectedDevice?.id == device.id {
                                        hidManager.disconnect()
                                        selectedDevice = nil
                                    } else {
                                        hidManager.connect(to: device)
                                        selectedDevice = device
                                    }
                                }
                                .buttonStyle(.borderedProminent)
                                .disabled(selectedDevice != nil && selectedDevice?.id != device.id)
                            }
                            .padding()
                            .background(selectedDevice?.id == device.id ? Color.blue.opacity(0.1) : Color.clear)
                            .cornerRadius(8)
                        }
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)

                // 控制按钮
                VStack(spacing: 15) {
                    HStack(spacing: 20) {
                        Button(hidManager.connectedDevices.isEmpty ? "开始扫描" : "停止扫描") {
                            if hidManager.connectedDevices.isEmpty {
                                hidManager.startScanning()
                            } else {
                                hidManager.stopScanning()
                            }
                        }
                        .buttonStyle(.bordered)

                        Button(isCapturingEnabled ? "停止捕获" : "开始捕获") {
                            toggleCapturing()
                        }
                        .buttonStyle(.borderedProminent)
                        .disabled(!hidManager.isConnected)
                    }
                }

                // 事件信息
                if isCapturingEnabled {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("实时事件")
                            .font(.headline)

                        VStack(alignment: .leading, spacing: 5) {
                            Text("键盘: \(keyboardCapture.lastKeyEvent)")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Text("鼠标: \(mouseCapture.lastMouseEvent)")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Text("位置: (\(Int(mouseCapture.mousePosition.x)), \(Int(mouseCapture.mousePosition.y)))")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(10)
                }

                Spacer()
            }
            .padding()
            .navigationTitle("MousePro")
        }
        .onAppear {
            hidManager.startScanning()
        }
        .onDisappear {
            hidManager.stopScanning()
            if isCapturingEnabled {
                stopCapturing()
            }
        }
    }

    private func toggleCapturing() {
        if isCapturingEnabled {
            stopCapturing()
        } else {
            startCapturing()
        }
    }

    private func startCapturing() {
        let keyboardSuccess = keyboardCapture.startCapturing()
        let mouseSuccess = mouseCapture.startCapturing()

        if keyboardSuccess && mouseSuccess {
            isCapturingEnabled = true
        }
    }

    private func stopCapturing() {
        keyboardCapture.stopCapturing()
        mouseCapture.stopCapturing()
        isCapturingEnabled = false
    }
}

#Preview {
    ContentView()
}
